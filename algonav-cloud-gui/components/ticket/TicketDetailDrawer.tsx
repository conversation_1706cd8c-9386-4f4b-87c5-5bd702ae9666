'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import PersonIcon from '@mui/icons-material/Person';
import { formatDistanceToNow } from 'date-fns';

interface Ticket {
  id: string;
  title: string;
  description?: string;
  status: 'open' | 'in_progress' | 'waiting_on_customer' | 'resolved' | 'closed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  creator: { id: string; email: string };
  assignee?: { id: string; email: string };
  ticket_targets?: Array<{ target_type: string; target_id: number }>;
  ticket_comments?: Array<{
    id: string;
    body: string;
    created_at: string;
    author: { id: string; email: string };
  }>;
  ticket_status_history?: Array<{
    id: string;
    old_status: string;
    new_status: string;
    changed_at: string;
    changed_by: { id: string; email: string };
  }>;
}

interface TicketDetailDrawerProps {
  open: boolean;
  ticket: Ticket | null;
  onClose: () => void;
  onStatusChange?: (ticketId: string, status: string) => void;
  onAddComment?: (ticketId: string, comment: string) => void;
  loading?: boolean;
}

const statusColors = {
  open: 'primary',
  in_progress: 'info',
  waiting_on_customer: 'warning',
  resolved: 'success',
  closed: 'default',
  cancelled: 'error'
} as const;

const priorityColors = {
  low: 'default',
  medium: 'primary',
  high: 'warning',
  urgent: 'error'
} as const;

const statusLabels = {
  open: 'Open',
  in_progress: 'In Progress',
  waiting_on_customer: 'Waiting on Customer',
  resolved: 'Resolved',
  closed: 'Closed',
  cancelled: 'Cancelled'
};

const priorityLabels = {
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  urgent: 'Urgent'
};

export function TicketDetailDrawer({
  open,
  ticket,
  onClose,
  onStatusChange,
  onAddComment,
  loading = false
}: TicketDetailDrawerProps) {
  const [newComment, setNewComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);

  const handleStatusChange = (newStatus: string) => {
    if (ticket && onStatusChange) {
      onStatusChange(ticket.id, newStatus);
    }
  };

  const handleAddComment = async () => {
    if (!ticket || !newComment.trim() || !onAddComment) return;

    setSubmittingComment(true);
    try {
      await onAddComment(ticket.id, newComment.trim());
      setNewComment('');
    } catch (error) {
      console.error('Failed to add comment:', error);
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      handleAddComment();
    }
  };

  if (!ticket) return null;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: { xs: '100%', sm: 600 },
          maxWidth: '100vw'
        }
      }}
    >
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6" component="h2">
              Ticket #{ticket.id}
            </Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Stack spacing={3}>
              {/* Title and Description */}
              <Box>
                <Typography variant="h5" gutterBottom>
                  {ticket.title}
                </Typography>
                {ticket.description && (
                  <Typography variant="body1" color="text.secondary">
                    {ticket.description}
                  </Typography>
                )}
              </Box>

              {/* Status and Priority */}
              <Stack direction="row" spacing={2}>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Status
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <Select
                      value={ticket.status}
                      onChange={(e) => handleStatusChange(e.target.value)}
                    >
                      <MenuItem value="open">Open</MenuItem>
                      <MenuItem value="in_progress">In Progress</MenuItem>
                      <MenuItem value="waiting_on_customer">Waiting on Customer</MenuItem>
                      <MenuItem value="resolved">Resolved</MenuItem>
                      <MenuItem value="closed">Closed</MenuItem>
                      <MenuItem value="cancelled">Cancelled</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Priority
                  </Typography>
                  <Chip
                    label={priorityLabels[ticket.priority]}
                    color={priorityColors[ticket.priority]}
                    size="small"
                    variant="outlined"
                  />
                </Box>
              </Stack>

              {/* Assignee and Creator */}
              <Stack direction="row" spacing={2}>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Assignee
                  </Typography>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Avatar sx={{ width: 24, height: 24 }}>
                      <PersonIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2">
                      {ticket.assignee ? ticket.assignee.email : 'Unassigned'}
                    </Typography>
                  </Stack>
                </Box>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Creator
                  </Typography>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Avatar sx={{ width: 24, height: 24 }}>
                      <PersonIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2">
                      {ticket.creator.email}
                    </Typography>
                  </Stack>
                </Box>
              </Stack>

              {/* Targets */}
              {ticket.ticket_targets && ticket.ticket_targets.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Related Items
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap">
                    {ticket.ticket_targets.map((target, index) => (
                      <Chip
                        key={index}
                        label={`${target.target_type} #${target.target_id}`}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Stack>
                </Box>
              )}

              <Divider />

              {/* Comments Section */}
              <Box>
                <Typography variant="h6" gutterBottom>
                  Comments
                </Typography>
                
                {/* Comments List */}
                <Stack spacing={2} sx={{ mb: 2 }}>
                  {ticket.ticket_comments?.map((comment) => (
                    <Paper key={comment.id} sx={{ p: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                        <Avatar sx={{ width: 24, height: 24 }}>
                          <PersonIcon fontSize="small" />
                        </Avatar>
                        <Typography variant="subtitle2">
                          {comment.author.email}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                        </Typography>
                      </Stack>
                      <Typography variant="body2">
                        {comment.body}
                      </Typography>
                    </Paper>
                  ))}
                </Stack>

                {/* Add Comment */}
                <Stack spacing={2}>
                  <TextField
                    multiline
                    rows={3}
                    placeholder="Add a comment... (Ctrl+Enter to submit)"
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    onKeyDown={handleKeyPress}
                    disabled={submittingComment}
                  />
                  <Button
                    variant="contained"
                    startIcon={submittingComment ? <CircularProgress size={16} /> : <SendIcon />}
                    onClick={handleAddComment}
                    disabled={!newComment.trim() || submittingComment}
                    sx={{ alignSelf: 'flex-start' }}
                  >
                    Add Comment
                  </Button>
                </Stack>
              </Box>
            </Stack>
          )}
        </Box>

        {/* Footer with timestamps */}
        <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="caption" color="text.secondary">
              Created: {new Date(ticket.created_at).toLocaleString()}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Updated: {new Date(ticket.updated_at).toLocaleString()}
            </Typography>
          </Stack>
        </Box>
      </Box>
    </Drawer>
  );
}
