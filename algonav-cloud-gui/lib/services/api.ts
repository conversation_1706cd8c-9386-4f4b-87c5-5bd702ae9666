import { Template, GUIComponent } from '../../components/template/types/template';

export const api = {
  // Datasets
  async getDatasets() {
    const response = await fetch('/api/datasets')
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch datasets')
    }
    return result
  },

  async getRecentDatasets(limit: number = 5) {
    const response = await fetch(`/api/datasets/recent?limit=${limit}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch recent datasets')
    }
    return result
  },

  async createDataset(data: any) {
    const response = await fetch('/api/datasets', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create dataset')
    }
    return result
  },

  async deleteDataset(id: string) {
    const response = await fetch(`/api/datasets?id=${id}`, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to delete dataset')
    }
    return result
  },

  // Templates
  async getTemplates() {
    const response = await fetch('/api/templates')
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch templates')
    }
    return result
  },
  async createTemplate(template: Partial<Template>): Promise<Template> {
    const response = await fetch('/api/templates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(template),
    })

    const json = await response.json()

    if (!json.success) {
      throw new Error(json.error || 'Failed to create template')
    }

    return json.data
  },

  async deleteTemplate(id: string): Promise<void> {
    const response = await fetch(`/api/templates/${id}`, {
      method: 'DELETE',
    })

    const json = await response.json()

    if (!json.success) {
      throw new Error(json.error || 'Failed to delete template')
    }
  },
  // Files
  async getFiles() {
    const response = await fetch('/api/file')
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch files')
    }
    return result
  },

  async uploadFile(formData: FormData) {
    const response = await fetch('/api/file', {
      method: 'POST',
      body: formData
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to upload file')
    }
    return result
  },

  async deleteFile(path: string) {
    const response = await fetch(`/api/file?path=${encodeURIComponent(path)}`, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to delete file')
    }
    return result
  },

  // Batches (shown as Jobs in frontend)
  async getJobs(page?: number, itemsPerPage?: number) {
    const params = new URLSearchParams()
    if (page) params.append('page', page.toString())
    if (itemsPerPage) params.append('itemsPerPage', itemsPerPage.toString())

    const response = await fetch(`/api/jobs?${params.toString()}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch jobs')
    }
    return result
  },

  async getRecentJobs(limit: number = 5) {
    const response = await fetch(`/api/jobs/recent?limit=${limit}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch recent jobs')
    }
    return result
  },

  async getJobById(id: string) {
    const response = await fetch(`/api/jobs/${id}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch job')
    }
    return result
  },

  async createJob(data: any) {
    const response = await fetch('/api/jobs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create job')
    }
    return result
  },

  // Jobs (shown as Tasks in frontend)
  async getTasksByJobId(jobId: string) {
    // Note: Still uses the old batchId parameter name for the API call
    const response = await fetch(`/api/tasks?jobId=${jobId}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch tasks for job')
    }
    return result
  },

  async createTask(data: any) {
    const response = await fetch('/api/tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create task')
    }
    return result
  },

  async downloadTaskResults(taskId: string) {
    // Note: Still uses the old /api/jobs endpoint
    const response = await fetch(`/api/tasks/${taskId}/download`)
    if (!response.ok) {
      throw new Error('Failed to download task results')
    }
    return response
  },

  // Single Dataset
  async getDataset(id: string) {
    const response = await fetch(`/api/datasets/${id}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch dataset')
    }
    return result
  },

  async updateDataset({ id, ...data }: { id: string, [key: string]: any }) {
    const response = await fetch(`/api/datasets/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to update dataset')
    }
    return result
  },



  async getDatasetFiles(datasetId: string) {
    const response = await fetch(`/api/dataset-files?datasetId=${datasetId}`)
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch dataset files')
    }
    return result
  },


 async getTemplate(id: string): Promise<Template> {
  const response = await fetch(`/api/templates/${id}`);
  const json = await response.json();

  if (!json.success) {
    throw new Error(json.error || 'Failed to fetch template');
  }

  return json.data;
},

async updateTemplate(id: string, template: Partial<Template>): Promise<Template> {
  const response = await fetch(`/api/templates/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(template),
  });

  const json = await response.json();

  if (!json.success) {
    throw new Error(json.error || 'Failed to update template');
  }

  return json.data;
},

async getGUIComponents(): Promise<GUIComponent[]> {
  const response = await fetch('/api/gui-components');
  const json = await response.json();

  if (!json.success) {
    throw new Error(json.error || 'Failed to fetch GUI components');
  }

  return json.data;
},

// Log Fields
async getLogFields() {
  const response = await fetch('/api/log-fields');
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch log fields');
  }

  return result.data;
},

// Tickets
async getTickets(filters: { status?: string; myTickets?: boolean; page?: number; limit?: number } = {}) {
  const params = new URLSearchParams();
  if (filters.status) params.append('status', filters.status);
  if (filters.myTickets) params.append('myTickets', 'true');
  if (filters.page) params.append('page', filters.page.toString());
  if (filters.limit) params.append('limit', filters.limit.toString());

  const response = await fetch(`/api/tickets?${params.toString()}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch tickets');
  }

  return result.data;
},

async getTicket(id: string) {
  const response = await fetch(`/api/tickets/${id}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch ticket');
  }

  return result.data;
},

async createTicket(data: {
  title: string;
  description?: string;
  priority?: string;
  assigneeId?: string;
  targets?: Array<{ target_type: string; target_id: number }>;
}) {
  const response = await fetch('/api/tickets', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to create ticket');
  }

  return result.data;
},

async updateTicket(id: string, data: {
  title?: string;
  description?: string;
  priority?: string;
  assigneeId?: string;
}) {
  const response = await fetch(`/api/tickets/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to update ticket');
  }

  return result.data;
},

async updateTicketStatus(id: string, status: string) {
  const response = await fetch(`/api/tickets/${id}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status })
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to update ticket status');
  }

  return result.data;
},

async getTicketComments(ticketId: string, page?: number, limit?: number) {
  const params = new URLSearchParams();
  if (page) params.append('page', page.toString());
  if (limit) params.append('limit', limit.toString());

  const response = await fetch(`/api/tickets/${ticketId}/comments?${params.toString()}`);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch ticket comments');
  }

  return result.data;
},

async addTicketComment(ticketId: string, body: string) {
  const response = await fetch(`/api/tickets/${ticketId}/comments`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ body })
  });
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to add comment');
  }

  return result.data;
}

}