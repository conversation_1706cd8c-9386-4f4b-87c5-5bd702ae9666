'use client';

import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Stack,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { TicketList } from '@/components/ticket/TicketList';
import { TicketDetailDrawer } from '@/components/ticket/TicketDetailDrawer';
import { CreateTicketDialog } from '@/components/ticket/CreateTicketDialog';
import { 
  useTickets, 
  useCreateTicket, 
  useUpdateTicketStatus, 
  useAddComment 
} from '@/lib/hooks/useTickets';

interface Ticket {
  id: string;
  title: string;
  description?: string;
  status: 'open' | 'in_progress' | 'waiting_on_customer' | 'resolved' | 'closed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  creator: { id: string; email: string };
  assignee?: { id: string; email: string };
  ticket_targets?: Array<{ target_type: string; target_id: number }>;
}

export default function TicketsPage() {
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Hooks
  const { data: ticketsData, isLoading, error } = useTickets({
    status: statusFilter,
    myTickets: true
  });
  const createTicket = useCreateTicket();
  const updateTicketStatus = useUpdateTicketStatus();
  const addComment = useAddComment();

  const tickets = ticketsData || [];

  const handleTicketClick = (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedTicket(null);
  };

  const handleCreateTicket = async (ticketData: any) => {
    await createTicket.mutateAsync(ticketData);
    setCreateDialogOpen(false);
  };

  const handleStatusChange = async (ticketId: string, status: string) => {
    await updateTicketStatus.mutateAsync({ id: ticketId, status });
  };

  const handleAddComment = async (ticketId: string, comment: string) => {
    await addComment.mutateAsync({ ticketId, body: comment });
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
  };

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">
          Failed to load tickets: {error.message}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Support Tickets
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your support requests and track their progress
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Ticket
        </Button>
      </Stack>

      <TicketList
        tickets={tickets}
        loading={isLoading}
        onTicketClick={handleTicketClick}
        onStatusFilter={handleStatusFilter}
        onSearch={handleSearch}
      />

      <TicketDetailDrawer
        open={drawerOpen}
        ticket={selectedTicket}
        onClose={handleCloseDrawer}
        onStatusChange={handleStatusChange}
        onAddComment={handleAddComment}
        loading={false}
      />

      <CreateTicketDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateTicket}
        loading={createTicket.isPending}
      />
    </Container>
  );
}
